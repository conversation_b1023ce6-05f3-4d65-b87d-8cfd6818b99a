/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/Custom/ComboBox/ComboBox.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
.ComboBox {
    /* padding-left: 10px; */
    /* padding-right: 10px; */
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.modalRow {
    cursor: pointer;
    transition: all .3s;
}

.modalRow:hover td {
    background-color: #2d6651  !important;
    color: white;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/Custom/AanzaDataTable/AanzaDataTable.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
table input {
    border: 1px solid rgb(190, 190, 190) !important;
    border-radius: 1px !important;
}

table button {
    border-radius: 1px !important;
}

input {
    border: 1px solid rgb(190, 190, 190) !important;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/Loader/Loader.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
.loaderContainer {
    background-color: transparent;
    height: 100vh;
    display: grid;
    place-items: center;
    position: relative;
}

.scene {
    position: relative;
    z-index: 2123123;
    height: 220px;
    width: 220px;
    display: grid;
    place-items: center;
}

.cube-wrapper {
    transform-style: preserve-3d;
    animation: bouncing 2s infinite;
}

.cube {
    transform-style: preserve-3d;
    transform: rotateX(45deg) rotateZ(45deg);
    animation: rotation 2s infinite;
}

.cube-faces {
    transform-style: preserve-3d;
    height: 80px;
    width: 80px;
    position: relative;
    transform-origin: 0 0;
    transform: translateX(0) translateY(0) translateZ(-40px);
}

.cube-face {
    position: absolute;
    inset: 0;
    background: #0e2f1c;
    border: solid 2px #3bac6a;
}

.cube-face.shadow {
    transform: translateZ(-80px);
    animation: bouncing-shadow 2s infinite;
}

.cube-face.top {
    transform: translateZ(80px);
}

.cube-face.front {
    transform-origin: 0 50%;
    transform: rotateY(-90deg);
}

.cube-face.back {
    transform-origin: 0 50%;
    transform: rotateY(-90deg) translateZ(-80px);
}

.cube-face.right {
    transform-origin: 50% 0;
    transform: rotateX(-90deg) translateY(-80px);
}

.cube-face.left {
    transform-origin: 50% 0;
    transform: rotateX(-90deg) translateY(-80px) translateZ(80px);
}

@keyframes rotation {
    0% {
        transform: rotateX(45deg) rotateY(0) rotateZ(45deg);
        animation-timing-function: cubic-bezier(0.17, 0.84, 0.44, 1);
    }

    50% {
        transform: rotateX(45deg) rotateY(0) rotateZ(225deg);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }

    100% {
        transform: rotateX(45deg) rotateY(0) rotateZ(405deg);
        animation-timing-function: cubic-bezier(0.17, 0.84, 0.44, 1);
    }
}

@keyframes bouncing {
    0% {
        transform: translateY(-40px);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }

    45% {
        transform: translateY(40px);
        animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }

    100% {
        transform: translateY(-40px);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }
}

@keyframes bouncing-shadow {
    0% {
        transform: translateZ(-80px) scale(1.3);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
        opacity: 0.05;
    }

    45% {
        transform: translateZ(0);
        animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
        opacity: 0.3;
    }

    100% {
        transform: translateZ(-80px) scale(1.3);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
        opacity: 0.05;
    }
}
