"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.jsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MainDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MainDashboard */ \"(app-pages-browser)/./src/app/dashboard/MainDashboard.jsx\");\n/* harmony import */ var _AssessorDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AssessorDashboard */ \"(app-pages-browser)/./src/app/dashboard/AssessorDashboard.jsx\");\n/* harmony import */ var _DeliveryDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeliveryDashboard */ \"(app-pages-browser)/./src/app/dashboard/DeliveryDashboard.jsx\");\n/* harmony import */ var _InstallerDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./InstallerDashboard */ \"(app-pages-browser)/./src/app/dashboard/InstallerDashboard.jsx\");\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _provider_UserContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const { userRole } = (0,_provider_UserContext__WEBPACK_IMPORTED_MODULE_7__.useUser)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"wrapper\",\n            children: userRole === \"Assessor\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssessorDashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                lineNumber: 18,\n                columnNumber: 25\n            }, undefined) : userRole === \"Delivery\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeliveryDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                lineNumber: 20,\n                columnNumber: 25\n            }, undefined) : userRole === \"Installer\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InstallerDashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                lineNumber: 22,\n                columnNumber: 25\n            }, undefined) : userRole === \"Admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MainDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                lineNumber: 24,\n                columnNumber: 25\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                h: \"100vh\",\n                align: \"center\",\n                justify: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Spinner, {\n                    size: \"xl\",\n                    color: \"green.500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 29\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n                lineNumber: 26,\n                columnNumber: 25\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.jsx\",\n            lineNumber: 15,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n_s(Dashboard, \"FbbzURxwN2u2PFfCEWvely7vqaw=\", false, function() {\n    return [\n        _provider_UserContext__WEBPACK_IMPORTED_MODULE_7__.useUser\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.jsx\n"));

/***/ })

});