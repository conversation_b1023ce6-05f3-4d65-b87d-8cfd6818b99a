"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/assessors-report/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/forms/assessors-report/follow-up/page.jsx":
/*!***********************************************************!*\
  !*** ./src/app/forms/assessors-report/follow-up/page.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Custom/ComboBox/ComboBox */ \"(app-pages-browser)/./src/components/Custom/ComboBox/ComboBox.jsx\");\n/* harmony import */ var _components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Custom/TableComboBox/TableComboBox */ \"(app-pages-browser)/./src/components/Custom/TableComboBox/TableComboBox.jsx\");\n/* harmony import */ var _components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Custom/AanzaDataTable/AanzaDataTable */ \"(app-pages-browser)/./src/components/Custom/AanzaDataTable/AanzaDataTable.jsx\");\n/* harmony import */ var _axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @components/Custom/NumberInput/NumberInput */ \"(app-pages-browser)/./src/components/Custom/NumberInput/NumberInput.jsx\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @utils/functions */ \"(app-pages-browser)/./src/app/utils/functions.js\");\n/* harmony import */ var _utils_constant__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @utils/constant */ \"(app-pages-browser)/./src/app/utils/constant.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader_Loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @components/Custom/MultipleImageUploader/MultipleImageUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleImageUploader/MultipleImageUploader.jsx\");\n/* harmony import */ var _components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @components/Custom/MultipleAudioUploader/MultipleAudioUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleAudioUploader/MultipleAudioUploader.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst QuotationFollowupHeaders = [\n    {\n        label: \"Item ID\",\n        key: \"Item_ID\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Item Description\",\n        key: \"Item_Title\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Qty\",\n        key: \"Qty\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Rate\",\n        key: \"Rate\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Disc %\",\n        key: \"Disc\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Installation Charges\",\n        key: \"InstallationCharges\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Total\",\n        key: \"Total\",\n        width: \"100px\",\n        isReadOnly: true,\n        type: \"number\"\n    },\n    {\n        label: \"Delivery Date\",\n        key: \"Date\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"date\"\n    },\n    {\n        label: \"Details\",\n        key: \"Details\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    }\n];\nconst createQuotationFollowupEmptyTableRow = ()=>[\n        {\n            Item_ID: \"\",\n            Item_Title: \"\",\n            Qty: 0,\n            Rate: 0,\n            InstallationCharges: 0,\n            Disc: 0,\n            Total: 0,\n            Date: \"\",\n            Details: \"\"\n        }\n    ];\nconst createQuotationFollowupInitialFormData = ()=>({\n        location: \"\",\n        vno: \"\",\n        vtp: \"\",\n        voucherNo: \"\",\n        date: \"\",\n        mnth: \"\",\n        tenderNo: \"\",\n        exchangeRate: 0,\n        partyRef: \"\",\n        attentionPerson: \"\",\n        designation: \"\",\n        contactPerson: \"\",\n        email: \"\",\n        phoneNumber: \"\",\n        landline: \"\",\n        address: \"\",\n        lead: \"\",\n        clientId: \"\",\n        clientTitle: \"\",\n        currency: \"\",\n        subject: \"\",\n        quotation: \"\",\n        totalAmount: 0,\n        freight: 0,\n        netAmount: 0,\n        validityDays: 0,\n        paymentTerms: \"\",\n        narration: \"\",\n        salesTaxR: \"\",\n        salesTaxA: \"\",\n        discountPercent: \"\",\n        discountAmount: 0,\n        netPayableAmt: 0,\n        sTaxAmount: 0,\n        assignerId: \"\",\n        assignerTitle: \"\",\n        assessmentTime: \"\",\n        assignerLocation: \"\"\n    });\nconst QuotationFollowupCalculationFields = [\n    \"salesTaxR\",\n    \"salesTaxA\",\n    \"netAmount\",\n    \"discountPercent\",\n    \"discountAmount\",\n    \"netPayableAmt\",\n    \"sTaxAmount\",\n    \"totalAmount\",\n    \"freight\"\n];\nconst RecoveryFollowUp = ()=>{\n    _s();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams)();\n    const [isDisabled, setIsDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tableData, setTableData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(createQuotationFollowupEmptyTableRow());\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(createQuotationFollowupInitialFormData());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [audios, setAudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadedImages, setLoadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadedAudios, setLoadedAudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleInputChange = (singleInput, bulkInput)=>{\n        if (singleInput) {\n            let { name: name1, value } = singleInput.target || singleInput;\n            if (QuotationFollowupCalculationFields.includes(name1)) {\n                setFormData((prev)=>{\n                    const salesTaxR = name1 === \"salesTaxR\" ? Number(value) : Number(prev.salesTaxR);\n                    const salesTaxA = name1 === \"salesTaxA\" ? Number(value) : Number(prev.salesTaxA);\n                    const freight = name1 === \"freight\" ? Number(value) : Number(prev.freight);\n                    const discountPercent = name1 === \"discountPercent\" ? value : prev.discountPercent;\n                    let discountAmount = name1 === \"discountAmount\" ? value : prev.discountAmount;\n                    const totalAmount = prev.totalAmount;\n                    let sTaxAmount = prev.sTaxAmount;\n                    let netAmount = prev.netAmount;\n                    if (salesTaxR + salesTaxA > 100) {\n                        sTaxAmount = 0;\n                    } else {\n                        const totalPercentage = (salesTaxR + salesTaxA) / 100;\n                        sTaxAmount = totalAmount * totalPercentage;\n                    }\n                    if (name1 !== \"netAmount\") {\n                        netAmount = totalAmount + sTaxAmount;\n                    }\n                    discountAmount = discountPercent / 100 * netAmount;\n                    const netPayableAmt = netAmount + freight - discountAmount;\n                    return {\n                        ...prev,\n                        [name1]: value,\n                        salesTaxR,\n                        salesTaxA,\n                        sTaxAmount,\n                        discountAmount,\n                        totalAmount,\n                        netAmount,\n                        netPayableAmt\n                    };\n                });\n            } else {\n                setFormData((prev)=>({\n                        ...prev,\n                        [name1]: value\n                    }));\n            }\n        } else if (bulkInput) {\n            setFormData((prev)=>({\n                    ...prev,\n                    ...bulkInput\n                }));\n        }\n    };\n    const transformData = function() {\n        let orderData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, itemsArray = arguments.length > 1 ? arguments[1] : void 0, isNavigationdata = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (isNavigationdata) {\n            return itemsArray.map((item)=>{\n                return {\n                    Item_ID: item === null || item === void 0 ? void 0 : item.item_id,\n                    Item_Title: item === null || item === void 0 ? void 0 : item.itemTitle,\n                    Rate: Number(item === null || item === void 0 ? void 0 : item.Rate),\n                    InstallationCharges: Number(item === null || item === void 0 ? void 0 : item.InstallationCharges),\n                    Disc: Number(item === null || item === void 0 ? void 0 : item.Discount),\n                    Total: Number(item === null || item === void 0 ? void 0 : item.Total),\n                    Date: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_7__.formatDate)(new Date(item === null || item === void 0 ? void 0 : item.VisitDate), true),\n                    Details: item === null || item === void 0 ? void 0 : item.details,\n                    Qty: Number(item === null || item === void 0 ? void 0 : item.Qty)\n                };\n            });\n        } else {\n            return itemsArray.map((item, index)=>{\n                return {\n                    Dated: orderData.Dated,\n                    VTP: orderData.VTP,\n                    Mnth: orderData.Mnth,\n                    Location: orderData.Location,\n                    vno: orderData.vno,\n                    srno: index + 1,\n                    item_id: item.Item_ID,\n                    Rate: Number(item.Rate),\n                    InstallationCharges: Number(item.InstallationCharges),\n                    Discount: Number(item.Disc),\n                    Total: Number(item.Total),\n                    VisitDate: item.Date,\n                    details: item.Details,\n                    Qty: Number(item.Qty)\n                };\n            });\n        }\n    };\n    const handleImagesChange = (newImages)=>{\n        setImages(newImages);\n    };\n    const handleAudiosChange = (newAudios)=>{\n        setAudios(newAudios);\n    };\n    const cellRender = (value, key, rowIndex, colIndex, cellData, handleInputChange)=>{\n        if ([\n            \"Item_ID\",\n            \"Item_Title\",\n            \"unit\"\n        ].includes(key)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                rowIndex: rowIndex,\n                colIndex: colIndex,\n                inputWidth: cellData.width,\n                value: value,\n                onChange: (val)=>handleInputChange(val),\n                modalData: items,\n                modalHeaders: [\n                    \"ID\",\n                    \"Title\"\n                ],\n                isDisabled: isDisabled\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Use NumberInput for number fields\n        if (cellData.type === \"number\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                width: cellData.width,\n                value: value,\n                onChange: (e)=>handleInputChange(e.target.value),\n                size: \"sm\",\n                isReadOnly: cellData.isReadOnly,\n                isDisabled: isDisabled\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Input, {\n            width: cellData.width,\n            value: value,\n            onChange: (e)=>handleInputChange(e.target.value),\n            size: \"sm\",\n            type: cellData.type,\n            isReadOnly: cellData.isReadOnly,\n            disabled: isDisabled\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, undefined);\n    };\n    const calculation = (header, value, rowIndex)=>{\n        setTableData((prevData)=>{\n            return prevData.map((r, i)=>{\n                if (i === rowIndex) {\n                    const updatedRow = {\n                        ...r,\n                        [header.key]: value\n                    };\n                    const qty = header.key === \"Qty\" ? value : r.Qty;\n                    const rate = header.key === \"Rate\" ? value : r.Rate;\n                    const installationCharges = header.key === \"InstallationCharges\" ? value : r.InstallationCharges;\n                    const discountPercent = header.key === \"Disc\" ? value : r.Disc;\n                    const total = (Number(qty) || 0) * (Number(rate) || 0);\n                    const discountAmount = discountPercent / 100 * total;\n                    updatedRow.Total = total - discountAmount + (Number(installationCharges) || 0);\n                    return updatedRow;\n                }\n                return r;\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let totalQty = 0;\n        let totalAmount = 0;\n        tableData.forEach((data)=>{\n            totalAmount += Number(data.Total) || 0;\n        });\n        const salesTaxR = formData.salesTaxR;\n        const salesTaxA = formData.salesTaxA;\n        const freight = formData.freight;\n        const discountPercent = formData.discountPercent;\n        let discountAmount = formData.discountAmount;\n        let sTaxAmount = formData.sTaxAmount;\n        let netAmount = formData.netAmount;\n        if (salesTaxR + salesTaxA > 100) {\n            sTaxAmount = 0;\n        } else {\n            const totalPercentage = (salesTaxR + salesTaxA) / 100;\n            sTaxAmount = totalAmount * totalPercentage;\n        }\n        if (name !== \"netAmount\") {\n            netAmount = totalAmount + sTaxAmount;\n        }\n        discountAmount = discountPercent / 100 * netAmount;\n        const netPayableAmt = netAmount + freight - discountAmount;\n        setFormData((prev)=>({\n                ...prev,\n                totalQty,\n                totalAmount,\n                netAmount,\n                netPayableAmt,\n                sTaxAmount,\n                discountAmount\n            }));\n    }, [\n        tableData\n    ]);\n    const navigateVoucherForm = async (voucherNo)=>{\n        setLoading(true);\n        setIsDisabled(true);\n        try {\n            const response = await _axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"offer/navigate\", {\n                goto: true,\n                voucher_no: voucherNo\n            });\n            const fileResponse = await _axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"offer/get-files\", {\n                params: {\n                    refVoucherNo: voucherNo\n                }\n            });\n            // Process files by type\n            const allFiles = fileResponse.data;\n            const images = allFiles.filter((file)=>file.Type === \"image\" && file.FileName !== \"signature.png\");\n            const audios = allFiles.filter((file)=>file.Type === \"audio\");\n            // Find signature if it exists\n            const signatureFile = allFiles.find((file)=>file.FileName === \"signature.png\");\n            if (signatureFile) {\n                // Convert buffer to base64 string for signature pad\n                const buffer = Buffer.from(signatureFile.FileData.data);\n                const base64String = \"data:image/png;base64,\".concat(buffer.toString(\"base64\"));\n                setSignature(base64String);\n            }\n            setLoadedAudios(audios);\n            setLoadedImages(images);\n            const resData = response.data;\n            const { items } = response.data;\n            var _resData_client_id, _resData_ClientName, _resData_EmployeeID, _resData_EmployeeName, _resData_AssessmentLocation, _resData_SalesTaxA, _resData_SalesTaxR, _resData_Discount, _resData_DiscountPercent, _resData_GrossAmount, _resData_Lead, _resData_TenderNo, _resData_AttentionPerson, _resData_AttentionPerson_Desig, _resData_ContactPerson, _resData_Currency_ID, _resData_Subject, _resData_Quotation, _resData_GrdTotalPSTAmt, _resData_Freight, _resData_Validity, _resData_Terms, _resData_NetAmount, _resData_StartingComments;\n            const form = {\n                date: (resData === null || resData === void 0 ? void 0 : resData.Dated) ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_7__.formatDate)(new Date(resData === null || resData === void 0 ? void 0 : resData.Dated), true) : null,\n                vtp: resData === null || resData === void 0 ? void 0 : resData.VTP,\n                mnth: resData === null || resData === void 0 ? void 0 : resData.Mnth,\n                location: resData === null || resData === void 0 ? void 0 : resData.Location,\n                vno: resData === null || resData === void 0 ? void 0 : resData.vno,\n                voucherNo: resData === null || resData === void 0 ? void 0 : resData.Voucher_No,\n                clientId: (_resData_client_id = resData === null || resData === void 0 ? void 0 : resData.client_id) !== null && _resData_client_id !== void 0 ? _resData_client_id : \"\",\n                clientTitle: (_resData_ClientName = resData === null || resData === void 0 ? void 0 : resData.ClientName) !== null && _resData_ClientName !== void 0 ? _resData_ClientName : \"\",\n                assignerId: (_resData_EmployeeID = resData === null || resData === void 0 ? void 0 : resData.EmployeeID) !== null && _resData_EmployeeID !== void 0 ? _resData_EmployeeID : \"\",\n                assignerTitle: (_resData_EmployeeName = resData === null || resData === void 0 ? void 0 : resData.EmployeeName) !== null && _resData_EmployeeName !== void 0 ? _resData_EmployeeName : \"\",\n                assessmentTime: (resData === null || resData === void 0 ? void 0 : resData.AssessmentTime) ? dayjs__WEBPACK_IMPORTED_MODULE_11___default()(resData === null || resData === void 0 ? void 0 : resData.AssessmentTime).format(\"YYYY-MM-DDTHH:mm\") : \"\",\n                assignerLocation: (_resData_AssessmentLocation = resData === null || resData === void 0 ? void 0 : resData.AssessmentLocation) !== null && _resData_AssessmentLocation !== void 0 ? _resData_AssessmentLocation : \"\",\n                salesTaxA: (_resData_SalesTaxA = resData === null || resData === void 0 ? void 0 : resData.SalesTaxA) !== null && _resData_SalesTaxA !== void 0 ? _resData_SalesTaxA : 0,\n                salesTaxR: (_resData_SalesTaxR = resData === null || resData === void 0 ? void 0 : resData.SalesTaxR) !== null && _resData_SalesTaxR !== void 0 ? _resData_SalesTaxR : 0,\n                discountAmount: (_resData_Discount = resData === null || resData === void 0 ? void 0 : resData.Discount) !== null && _resData_Discount !== void 0 ? _resData_Discount : 0,\n                discountPercent: (_resData_DiscountPercent = resData === null || resData === void 0 ? void 0 : resData.DiscountPercent) !== null && _resData_DiscountPercent !== void 0 ? _resData_DiscountPercent : 0,\n                netPayableAmt: (_resData_GrossAmount = resData === null || resData === void 0 ? void 0 : resData.GrossAmount) !== null && _resData_GrossAmount !== void 0 ? _resData_GrossAmount : 0,\n                lead: (_resData_Lead = resData === null || resData === void 0 ? void 0 : resData.Lead) !== null && _resData_Lead !== void 0 ? _resData_Lead : \"\",\n                tenderNo: (_resData_TenderNo = resData === null || resData === void 0 ? void 0 : resData.TenderNo) !== null && _resData_TenderNo !== void 0 ? _resData_TenderNo : \"\",\n                // exchangeRate: resData?.ExchRate ?? 0,\n                // partyRef: resData?.Party_ref ?? \"\",\n                attentionPerson: (_resData_AttentionPerson = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson) !== null && _resData_AttentionPerson !== void 0 ? _resData_AttentionPerson : \"\",\n                designation: (_resData_AttentionPerson_Desig = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson_Desig) !== null && _resData_AttentionPerson_Desig !== void 0 ? _resData_AttentionPerson_Desig : \"\",\n                contactPerson: (_resData_ContactPerson = resData === null || resData === void 0 ? void 0 : resData.ContactPerson) !== null && _resData_ContactPerson !== void 0 ? _resData_ContactPerson : \"\",\n                currency: (_resData_Currency_ID = resData === null || resData === void 0 ? void 0 : resData.Currency_ID) !== null && _resData_Currency_ID !== void 0 ? _resData_Currency_ID : \"\",\n                subject: (_resData_Subject = resData === null || resData === void 0 ? void 0 : resData.Subject) !== null && _resData_Subject !== void 0 ? _resData_Subject : \"\",\n                quotation: (_resData_Quotation = resData === null || resData === void 0 ? void 0 : resData.Quotation) !== null && _resData_Quotation !== void 0 ? _resData_Quotation : \"\",\n                totalAmount: (_resData_GrdTotalPSTAmt = resData === null || resData === void 0 ? void 0 : resData.GrdTotalPSTAmt) !== null && _resData_GrdTotalPSTAmt !== void 0 ? _resData_GrdTotalPSTAmt : 0,\n                freight: (_resData_Freight = resData === null || resData === void 0 ? void 0 : resData.Freight) !== null && _resData_Freight !== void 0 ? _resData_Freight : 0,\n                validityDays: (_resData_Validity = resData === null || resData === void 0 ? void 0 : resData.Validity) !== null && _resData_Validity !== void 0 ? _resData_Validity : 0,\n                paymentTerms: (_resData_Terms = resData === null || resData === void 0 ? void 0 : resData.Terms) !== null && _resData_Terms !== void 0 ? _resData_Terms : \"\",\n                netAmount: (_resData_NetAmount = resData === null || resData === void 0 ? void 0 : resData.NetAmount) !== null && _resData_NetAmount !== void 0 ? _resData_NetAmount : 0,\n                narration: (_resData_StartingComments = resData === null || resData === void 0 ? void 0 : resData.StartingComments) !== null && _resData_StartingComments !== void 0 ? _resData_StartingComments : \"\"\n            };\n            setTableData(()=>transformData([], items, true));\n            setFormData((prev)=>({\n                    ...prev,\n                    ...form\n                }));\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching goto voucher:\", error);\n            setLoading(false);\n            toast({\n                title: \"goto Voucher Fetching Failed !\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const loadInitialData = async ()=>{\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const { data: purposeData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_7__.getData)(\"purpose/\" + purpose_no);\n        if (!purposeData.RefVoucherNo) return;\n        navigateVoucherForm(purposeData.RefVoucherNo);\n        const { data: clientData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_7__.getData)(\"client/\" + (purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID));\n        const items = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_7__.getData)(\"getRecords/items\");\n        setFormData((prev)=>({\n                ...prev,\n                assignerId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessorID,\n                assessmentTime: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessmentTime,\n                assignerLocation: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                address: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                email: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientEmail,\n                phoneNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientMobileNo,\n                landlineNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTelephone,\n                clientId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID,\n                clientTitle: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTitle,\n                contact: purposeData === null || purposeData === void 0 ? void 0 : purposeData.contactPerson,\n                comments: purposeData === null || purposeData === void 0 ? void 0 : purposeData.remarks,\n                ...clientData\n            }));\n        const itemData = [];\n        items.map((item)=>{\n            itemData.push({\n                Item_ID: item.id,\n                Item_Title: item.Title,\n                Item_Unit: item.Unit,\n                Item_Details: item.Details,\n                Item_Sale_Rate: item.Sale_Rate\n            });\n        });\n        setItems(itemData);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n            lineNumber: 517,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-inner\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bgWhite\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                margin: \"0\",\n                                                textAlign: \"center\",\n                                                color: \"#2B6CB0\",\n                                                fontSize: \"20px\",\n                                                fontWeight: \"bold\",\n                                                padding: \"10px\"\n                                            },\n                                            children: \"Site Assessment Follow-Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    style: {\n                                        gap: \"10px\",\n                                        paddingTop: \"8px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\"\n                                                }\n                                            },\n                                            className: \"bgWhite col-md-5 col-sm-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        margin: \"0\",\n                                                        textAlign: \"center\",\n                                                        color: \"#2B6CB0\",\n                                                        fontSize: \"20px\",\n                                                        fontWeight: \"bold\"\n                                                    },\n                                                    children: \"SOP's\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"question\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"1. Is the property more than 2 years old ?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                            name: \"question1\",\n                                                            placeholder: \"Select\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Yes\",\n                                                                    children: \"Yes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"No\",\n                                                                    children: \"No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"question\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"2. Is the combined yearly income of the household equal to or less than 180,000 ?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                            name: \"question1\",\n                                                            placeholder: \"Select\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Yes\",\n                                                                    children: \"Yes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"No\",\n                                                                    children: \"No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"question\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"3. Are you ready to decommission the ducted gas heated system ?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                            name: \"question1\",\n                                                            placeholder: \"Select\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Yes\",\n                                                                    children: \"Yes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"No\",\n                                                                    children: \"No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\"\n                                                }\n                                            },\n                                            className: \"bgWhite col-md-5 col-sm-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexWrap: {\n                                                        base: \"wrap\",\n                                                        sm: \"wrap\",\n                                                        md: \"wrap\",\n                                                        lg: \"nowrap\"\n                                                    },\n                                                    gap: \"10px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                    sx: {\n                                                        width: {\n                                                            base: \"100%\",\n                                                            sm: \"100%\",\n                                                            md: \"100%\",\n                                                            lg: \"100%\"\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                        className: \"\",\n                                                        sx: {\n                                                            display: \"grid\",\n                                                            gridTemplateColumns: {\n                                                                base: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                sm: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                md: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                            },\n                                                            gap: \"5px\"\n                                                        },\n                                                        children: _utils_constant__WEBPACK_IMPORTED_MODULE_8__.RecoveryFollowUpSectionFormFields[0].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    flexWrap: {\n                                                                        base: \"wrap\",\n                                                                        sm: \"wrap\",\n                                                                        md: \"wrap\",\n                                                                        lg: \"nowrap\"\n                                                                    },\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        md: \"row\"\n                                                                    },\n                                                                    marginTop: \"10px\"\n                                                                },\n                                                                isRequired: field.isRequired,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                        htmlFor: field.id,\n                                                                        sx: {\n                                                                            marginBottom: \"0\",\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                md: \"100%\",\n                                                                                lg: \"27%\"\n                                                                            }\n                                                                        },\n                                                                        children: field.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 35\n                                                                    }, undefined),\n                                                                    field.type === \"date\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                                                        id: field.id,\n                                                                        name: field.name,\n                                                                        type: field.type,\n                                                                        value: formData[field.value],\n                                                                        onChange: handleInputChange,\n                                                                        placeholder: field.placeholder,\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        readOnly: field.isReadOnly,\n                                                                        // min={field.minDate}\n                                                                        // max={field.maxDate}\n                                                                        disabled: isDisabled,\n                                                                        sx: {\n                                                                            marginLeft: {\n                                                                                base: \"0\",\n                                                                                sm: \"0\",\n                                                                                lg: \"4px\"\n                                                                            },\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                md: \"100%\",\n                                                                                lg: \"80%\"\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                                                        id: field.id,\n                                                                        name: field.name,\n                                                                        type: field.type,\n                                                                        value: formData[field.value],\n                                                                        onChange: handleInputChange,\n                                                                        placeholder: field.placeholder,\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        readOnly: field.isReadOnly,\n                                                                        disabled: field.name === \"voucherNo\" ? isDisabled : isDisabled,\n                                                                        sx: {\n                                                                            marginLeft: {\n                                                                                base: \"0\",\n                                                                                sm: \"0\",\n                                                                                lg: \"4px\"\n                                                                            },\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                md: \"100%\",\n                                                                                lg: \"80%\"\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                        lineNumber: 702,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                ]\n                                                            }, field.id, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                className: \"pt-4 pb-4\",\n                                                sx: {\n                                                    display: \"grid\",\n                                                    gridTemplateColumns: {\n                                                        base: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                        sm: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                        md: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                        lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                    },\n                                                    gap: \"5px\"\n                                                },\n                                                children: _utils_constant__WEBPACK_IMPORTED_MODULE_8__.RecoveryFollowUpSectionFormFields[1].map((control, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            flexDirection: {\n                                                                base: \"column\",\n                                                                sm: \"column\",\n                                                                lg: \"row\"\n                                                            },\n                                                            marginTop: \"10px\",\n                                                            flexWrap: \"nowrap\"\n                                                        },\n                                                        isRequired: control.isRequired,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                htmlFor: control.fields[0].name,\n                                                                sx: {\n                                                                    width: {\n                                                                        base: \"100%\",\n                                                                        sm: \"100%\",\n                                                                        lg: \"20%\"\n                                                                    }\n                                                                },\n                                                                children: control.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                                sx: {\n                                                                    width: {\n                                                                        base: \"100%\",\n                                                                        sm: \"100%\",\n                                                                        lg: \"80%\"\n                                                                    },\n                                                                    display: \"flex\",\n                                                                    gap: control.fields.length > 1 ? \"10px\" : \"0\"\n                                                                },\n                                                                children: control.fields.map((field, fieldIndex)=>field.component === \"ComboBox\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        target: true,\n                                                                        onChange: handleInputChange,\n                                                                        name: field.name,\n                                                                        inputWidths: field.inputWidths,\n                                                                        buttonWidth: field.buttonWidth,\n                                                                        styleButton: {\n                                                                            padding: \"3px !important\"\n                                                                        },\n                                                                        tableData: [],\n                                                                        tableHeaders: field.tableHeaders,\n                                                                        nameFields: field.nameFields,\n                                                                        placeholders: field.placeholders,\n                                                                        keys: field.keys,\n                                                                        form: formData,\n                                                                        isDisabled: true\n                                                                    }, fieldIndex, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                                                        onChange: handleInputChange,\n                                                                        name: field.name,\n                                                                        placeholder: field.placeholder,\n                                                                        value: formData[field.value],\n                                                                        _placeholder: field._placeholder,\n                                                                        type: field.type,\n                                                                        style: {\n                                                                            width: field.inputWidth\n                                                                        },\n                                                                        disabled: true\n                                                                    }, fieldIndex, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 37\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 29\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                tableData: tableData,\n                                                setTableData: setTableData,\n                                                headers: QuotationFollowupHeaders,\n                                                tableWidth: \"100%\",\n                                                tableHeight: \"400px\",\n                                                fontSize: \"lg\",\n                                                cellRender: cellRender,\n                                                styleHead: {\n                                                    background: \"#3275bb\",\n                                                    color: \"white !important\"\n                                                },\n                                                styleBody: {\n                                                    background: \"white !important\"\n                                                },\n                                                calculation: calculation,\n                                                isDisabled: isDisabled\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\",\n                                                    lg: \"calc(50% - 5px) !important\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"flex-start\",\n                                                    flexDirection: {\n                                                        base: \"column\",\n                                                        sm: \"column\",\n                                                        lg: \"row\"\n                                                    },\n                                                    marginTop: \"10px\",\n                                                    flexWrap: \"nowrap\",\n                                                    height: \"100%\"\n                                                },\n                                                isRequired: \"true\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                    sx: {\n                                                        width: {\n                                                            base: \"100%\",\n                                                            sm: \"100%\",\n                                                            lg: \"100%\"\n                                                        },\n                                                        display: \"flex\",\n                                                        height: \"100%\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        initial: loadedImages,\n                                                        onChange: handleImagesChange,\n                                                        disabled: isDisabled\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\",\n                                                    lg: \"calc(50% - 5px) !important\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"flex-start\",\n                                                    flexDirection: {\n                                                        base: \"column\",\n                                                        sm: \"column\",\n                                                        lg: \"row\"\n                                                    },\n                                                    marginTop: \"10px\",\n                                                    flexWrap: \"nowrap\",\n                                                    height: \"100%\"\n                                                },\n                                                isRequired: \"true\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                    sx: {\n                                                        width: {\n                                                            base: \"100%\",\n                                                            sm: \"100%\",\n                                                            lg: \"100%\"\n                                                        },\n                                                        display: \"flex\",\n                                                        height: \"100%\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        initial: loadedAudios,\n                                                        onChange: handleAudiosChange,\n                                                        disabled: isDisabled\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-4 pb-2\",\n                                                style: {\n                                                    display: \"grid\",\n                                                    gridTemplateColumns: \"repeat(auto-fit,minmax(300px,1fr))\",\n                                                    gap: \"5px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Total Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"totalAmount\",\n                                                                    value: formData.totalAmount,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: true,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"S.Tax%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        },\n                                                                        display: \"flex\",\n                                                                        gap: 1.5\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            onChange: handleInputChange,\n                                                                            name: \"salesTaxR\",\n                                                                            value: formData.salesTaxR,\n                                                                            placeholder: \"R\",\n                                                                            _placeholder: {\n                                                                                color: \"gray.500\"\n                                                                            },\n                                                                            sx: {\n                                                                                width: {\n                                                                                    base: \"30%\",\n                                                                                    sm: \"30%\",\n                                                                                    lg: \"30%\"\n                                                                                }\n                                                                            },\n                                                                            isReadOnly: false,\n                                                                            isDisabled: isDisabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                            lineNumber: 1050,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            onChange: handleInputChange,\n                                                                            name: \"salesTaxA\",\n                                                                            value: formData.salesTaxA,\n                                                                            placeholder: \"A\",\n                                                                            _placeholder: {\n                                                                                color: \"gray.500\"\n                                                                            },\n                                                                            sx: {\n                                                                                width: {\n                                                                                    base: \"70%\",\n                                                                                    sm: \"70%\",\n                                                                                    lg: \"70%\"\n                                                                                }\n                                                                            },\n                                                                            isReadOnly: false,\n                                                                            isDisabled: isDisabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1039,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"S.Tax Amt.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1098,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"sTaxAmount\",\n                                                                    value: formData.sTaxAmount,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: true,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Net Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1140,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"netAmount\",\n                                                                    value: formData.netAmount,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: true,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1151,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1128,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Discount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        },\n                                                                        display: \"flex\",\n                                                                        gap: 1.5\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            onChange: handleInputChange,\n                                                                            name: \"discountPercent\",\n                                                                            value: formData.discountPercent,\n                                                                            placeholder: \"%\",\n                                                                            _placeholder: {\n                                                                                color: \"gray.500\"\n                                                                            },\n                                                                            sx: {\n                                                                                width: {\n                                                                                    base: \"30%\",\n                                                                                    sm: \"30%\",\n                                                                                    lg: \"30%\"\n                                                                                }\n                                                                            },\n                                                                            isReadOnly: false,\n                                                                            isDisabled: isDisabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                            lineNumber: 1204,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            onChange: handleInputChange,\n                                                                            name: \"discountAmount\",\n                                                                            value: formData.discountAmount,\n                                                                            placeholder: \"A\",\n                                                                            _placeholder: {\n                                                                                color: \"gray.500\"\n                                                                            },\n                                                                            sx: {\n                                                                                width: {\n                                                                                    base: \"70%\",\n                                                                                    sm: \"70%\",\n                                                                                    lg: \"70%\"\n                                                                                }\n                                                                            },\n                                                                            isReadOnly: true,\n                                                                            isDisabled: isDisabled\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                            lineNumber: 1220,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Freight\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1252,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"freight\",\n                                                                    value: formData.freight,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: false,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1240,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1239,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Net Payable Amt\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1294,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"netPayableAmt\",\n                                                                    value: formData.netPayableAmt,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: true,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1305,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1282,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"30%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Validity Days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1336,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"validityDays\",\n                                                                    value: formData.validityDays,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"70%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: false,\n                                                                    isDisabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1347,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1324,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1323,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                        gridColumn: \"1 / -1\",\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"4px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"flex-start\",\n                                                                width: \"100%\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"10%\"\n                                                                        }\n                                                                    },\n                                                                    children: \"Payment Terms\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1381,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                                                    onChange: handleInputChange,\n                                                                    name: \"paymentTerms\",\n                                                                    value: formData.paymentTerms,\n                                                                    placeholder: \"\",\n                                                                    _placeholder: {\n                                                                        color: \"gray.500\"\n                                                                    },\n                                                                    type: \"text\",\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"90%\"\n                                                                        }\n                                                                    },\n                                                                    isReadOnly: false,\n                                                                    disabled: isDisabled\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                                    lineNumber: 1392,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1369,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\",\n                                                    lg: \"calc(75% - 5px) !important\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    marginTop: \"10px\",\n                                                    flexDirection: {\n                                                        base: \"column\",\n                                                        sm: \"column\"\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                        sx: {\n                                                            width: \"100%\"\n                                                        },\n                                                        children: \"Remarks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1434,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Textarea, {\n                                                        _placeholder: {\n                                                            color: \"gray.500\"\n                                                        },\n                                                        resize: \"vertical\",\n                                                        sx: {\n                                                            width: \"100%\",\n                                                            height: \"100%\"\n                                                        },\n                                                        onChange: handleInputChange,\n                                                        name: \"narration\",\n                                                        value: formData.narration,\n                                                        disabled: isDisabled,\n                                                        rows: 10\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1441,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 1413,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                            sx: {\n                                                padding: \"15px\",\n                                                width: {\n                                                    base: \"100% !important\",\n                                                    sm: \"100%\",\n                                                    lg: \"calc(25% - 5px) !important\"\n                                                }\n                                            },\n                                            className: \"ClientDIVVV bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.FormControl, {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    marginTop: \"10px\",\n                                                    flexDirection: {\n                                                        base: \"column\",\n                                                        sm: \"column\"\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.FormLabel, {\n                                                        sx: {\n                                                            width: \"100%\"\n                                                        },\n                                                        children: \"Digital Signature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1477,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                        sx: {\n                                                            width: \"100%\",\n                                                            display: \"flex\",\n                                                            flexDirection: \"column\",\n                                                            alignItems: \"center\"\n                                                        },\n                                                        children: signature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            src: signature,\n                                                            alt: \"Client's Signature\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                            lineNumber: 1493,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                        lineNumber: 1484,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                            lineNumber: 1456,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                            lineNumber: 523,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                        lineNumber: 522,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                    lineNumber: 521,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n                lineNumber: 520,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false)\n    }, void 0, false);\n};\n_s(RecoveryFollowUp, \"u3u+H9xxt0FLWdw1UvnPO4/+AE0=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams\n    ];\n});\n_c = RecoveryFollowUp;\nconst RecoveryFollowUpPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n            lineNumber: 1510,\n            columnNumber: 23\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecoveryFollowUp, {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n            lineNumber: 1511,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\assessors-report\\\\follow-up\\\\page.jsx\",\n        lineNumber: 1510,\n        columnNumber: 3\n    }, undefined);\n_c1 = RecoveryFollowUpPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RecoveryFollowUpPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"RecoveryFollowUp\");\n$RefreshReg$(_c1, \"RecoveryFollowUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/assessors-report/follow-up/page.jsx\n"));

/***/ })

});