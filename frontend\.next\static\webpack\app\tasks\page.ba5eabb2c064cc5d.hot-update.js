"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/ViewLeadDetailsModal.jsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/ViewLeadDetailsModal.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/icon/icon.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FaDollarSign,FaEnvelope,FaGlobe,FaMapMarkerAlt,FaMobile,FaPhone,FaTags,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n\n\n\n\nconst ViewLeadDetailsModal = (param)=>{\n    let { isOpen, onClose, clientData } = param;\n    if (!clientData) return null;\n    const InfoRow = (param)=>{\n        let { icon, label, value } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n            spacing: 3,\n            align: \"start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                    as: icon,\n                    color: \"green.500\",\n                    mt: 1\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    flex: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fontSize: \"sm\",\n                            fontWeight: \"semibold\",\n                            color: \"gray.600\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                            lineNumber: 28,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fontSize: \"md\",\n                            color: \"gray.800\",\n                            children: value || \"N/A\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                            lineNumber: 31,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n            lineNumber: 25,\n            columnNumber: 5\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalHeader, {\n                        style: {\n                            background: \"#2d6651\",\n                            color: \"white\"\n                        },\n                        children: \"Lead Details\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalCloseButton, {\n                        style: {\n                            color: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalBody, {\n                        py: 6,\n                        maxHeight: \"60vh\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                            spacing: 4,\n                            align: \"stretch\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaUser,\n                                    label: \"Client Name\",\n                                    value: clientData.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    color: \"gray.700\",\n                                    children: \"Contact Information\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaPhone,\n                                    label: \"Phone\",\n                                    value: clientData.phone\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaMobile,\n                                    label: \"Mobile\",\n                                    value: clientData.mobile\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaEnvelope,\n                                    label: \"Email\",\n                                    value: clientData.email\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    color: \"gray.700\",\n                                    children: \"Business Information\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaGlobe,\n                                    label: \"Website\",\n                                    value: clientData.website\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaGlobe,\n                                    label: \"Portal Link\",\n                                    value: clientData.portallink\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaDollarSign,\n                                    label: \"Currency\",\n                                    value: clientData.currency\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    color: \"gray.700\",\n                                    children: \"Additional Information\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaMapMarkerAlt,\n                                    label: \"Shipping Address\",\n                                    value: clientData.shipping\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined),\n                                clientData.tags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                    spacing: 3,\n                                    align: \"start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                            as: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaTags,\n                                            color: \"green.500\",\n                                            mt: 1\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            flex: 1,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                    fontSize: \"sm\",\n                                                    fontWeight: \"semibold\",\n                                                    color: \"gray.600\",\n                                                    children: \"Tags\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Badge, {\n                                                    bg: \"#2d6651\",\n                                                    color: \"white\",\n                                                    _hover: {\n                                                        bg: \"#3a866a\"\n                                                    },\n                                                    variant: \"subtle\",\n                                                    children: clientData.tags\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    icon: _barrel_optimize_names_FaDollarSign_FaEnvelope_FaGlobe_FaMapMarkerAlt_FaMobile_FaPhone_FaTags_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaUser,\n                                    label: \"Client ID\",\n                                    value: clientData.id\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.ModalFooter, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: onClose,\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\dashboard\\\\ViewLeadDetailsModal.jsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ViewLeadDetailsModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ViewLeadDetailsModal);\nvar _c;\n$RefreshReg$(_c, \"ViewLeadDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ViewLeadDetailsModal.jsx\n"));

/***/ })

});